#!/usr/bin/env node
import fs from "fs";
import path from "path";
import os from "os";
import axios from "axios";
import readline from "readline";
import { spawn } from "child_process";
import { fileURLToPath } from "url";
import chalk from "chalk";

// --- 环境与常量设置 ---

// 抑制Node.js警告，禁用不必要的网络流量和遥测
process.env.NODE_NO_WARNINGS = "1";
process.env.CLAUDE_CODE_DISABLE_NONESSENTIAL_TRAFFIC = "1";
process.env.DISABLE_TELEMETRY = "1";
process.env.DISABLE_AUTOUPDATER = "1";


let hasArg = false;
const log = (...args) => {
    if (!hasArg) {
        console.log(...args);
    }
};

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, "package.json"), "utf-8"));

const SERVER_URL = process.env.API_SERVER_URL || "https://code.yoretea.com";
const userArgs = process.argv.slice(2);
const CONFIG_DIR = path.join(os.homedir(), ".claudecode");
const CONFIG_FILE = path.join(CONFIG_DIR, "config");

// --- 核心功能函数 ---

// --- 认证令牌管理 ---

/**
 * 从本地配置文件读取令牌
 * @returns {object|null}
 */
function readToken() {
    try {
        if (!fs.existsSync(CONFIG_FILE)) {
            return null;
        }
        const content = fs.readFileSync(CONFIG_FILE, "utf-8");
        const config = JSON.parse(content);
        return config.accessToken ? config : null;
    } catch (error) {
        console.error(chalk.red("读取令牌时出错:"), error.message);
        return null;
    }
}

/**
 * 将令牌保存到本地配置文件
 * @param {object} tokenData
 * @returns {boolean}
 */
function saveToken(tokenData) {
    try {
        if (!fs.existsSync(CONFIG_DIR)) {
            fs.mkdirSync(CONFIG_DIR, { recursive: true });
        }
        fs.writeFileSync(CONFIG_FILE, JSON.stringify(tokenData, null, 2));
        return true;
    } catch (error) {
        console.error(chalk.red("保存令牌时出错:"), error.message);
        return false;
    }
}

/**
 * 清除本地的令牌文件
 */
function clearToken() {
    try {
        if (fs.existsSync(CONFIG_FILE)) {
            fs.unlinkSync(CONFIG_FILE);
        }
    } catch (error) {
        console.error(chalk.red("清除令牌时出错:"), error.message);
    }
}

/**
 * 验证令牌是否有效
 * @param {object} tokenData
 * @returns {Promise<boolean>}
 */
async function verifyToken(tokenData) {
    try {
        const response = await axios.get(`${SERVER_URL}/api/auth/me`, {
            headers: { Authorization: `Bearer ${tokenData.accessToken}` },
        });
        return response.data.success;
    } catch (error) {
        return false;
    }
}


// --- 核心流程 ---

/**
 * 启动登录码认证流程
 * @returns {Promise<object>}
 */
async function startLoginCodeFlow() {
    return new Promise((resolve, reject) => {
        const rl = readline.createInterface({ input: process.stdin, output: process.stdout });
        
        console.log(chalk.cyan("🔐 请输入登录码进行认证"));
        console.log(chalk.gray("💡 您可以在已登录的设备上生成登录码"));
        console.log();
        
        const promptForCode = () => {
            rl.question(chalk.cyan("请输入登录码: "), async (loginCode) => {
                const code = loginCode.trim();
                if (!code) {
                    console.log(chalk.yellow("⚠️  登录码不能为空，请重新输入"));
                    promptForCode();
                    return;
                }

                try {
                    console.log(chalk.cyan("🔐 正在验证登录码..."));
                    const response = await axios.post(`${SERVER_URL}/api/auth/login-with-code`, {
                        loginCode: code
                    }, {
                        headers: { "User-Agent": `CODE CLI Client/${os.platform()}` }
                    });

                    if (response.data.success) {
                        const { user, accessToken } = response.data.data;
                        console.log("\n✅ 登录成功!");
                        console.log(`👤 用户: ${user.name} (${user.email})`);
                        
                        const tokenData = { 
                            ...user, 
                            accessToken,
                            authorized: true, 
                            timestamp: new Date().toISOString() 
                        };
                        
                        if (saveToken(tokenData)) {
                            console.log(chalk.green(`💾 配置已保存到: ${CONFIG_FILE}`));
                        }
                        rl.close();
                        resolve(tokenData);
                    } else {
                        console.error(chalk.red("验证失败: 无效的登录码"));
                        console.log("请重新输入:");
                        promptForCode();
                    }
                } catch (error) {
                    console.error(chalk.red("验证失败:"), error.response?.data?.message || error.message);
                    console.log("请重新输入:");
                    promptForCode();
                }
            });
        };

        promptForCode();
        
        // 设置超时
        const timeout = setTimeout(() => {
            rl.close();
            reject(new Error("登录超时"));
        }, 300000); // 5分钟超时

        rl.on('close', () => {
            clearTimeout(timeout);
        });
    });
}

/**
 * 检查CLI工具是否有新版本
 * @returns {Promise<string|null>}
 */
async function checkVersion() {
    try {
        const response = await axios.get(`${SERVER_URL}/version`, {
            headers: { "User-Agent": `CODE CLI Client/${os.platform()}` },
        });
        return response.data;
    } catch (error) {
        return null;
    }
}

/**
 * 检查是否存在有效的本地授权
 * @returns {Promise<object|false>}
 */
async function checkExistingAuth() {
    const tokenData = readToken();
    if (!tokenData) {
        return false;
    }

    if (await verifyToken(tokenData)) {
        log(chalk.green("✅ 发现有效的授权配置"));
        log(`👤 用户: ${tokenData.name} (${tokenData.email})`);
        log();
        return tokenData;
    } else {
        console.log(chalk.yellow("⚠️  令牌无效，需要重新授权"));
        clearToken();
        return false;
    }
}

/**
 * 认证成功后，执行主CLI程序
 * @param {object} authData
 */
async function onAuthenticated(authData) {
    log(chalk.green("\n🎊 CLI客户端已准备就绪!"));
    log();
    log(chalk.green("✨ 授权流程完成!"));
    log(chalk.gray(`💡 使用 ${chalk.bold("claude --logout")} 退出登录`));
    log();

    const env = {
        ...process.env,
        ANTHROPIC_AUTH_TOKEN: authData.accessToken,
        USER_ID: authData.name,
        USER_EMAIL: authData.email,
        ANTHROPIC_BASE_URL: SERVER_URL + "/claude",
    };

    const cliScriptPath = path.resolve(__dirname, "cli.js");
    const child = spawn("node", [cliScriptPath, ...userArgs], { env, stdio: "inherit" });

    child.on("close", (code) => {
        process.exit(code);
    });
    child.on("error", (err) => {
        throw err;
    });
}

/**
 * 程序主入口
 */
async function main() {
    if (userArgs.includes("--logout")) {
        clearToken();
        log(chalk.green("✅ 退出登录成功"));
        process.exit(0);
    }

    hasArg = userArgs.length > 0;
    log(chalk.bold.cyan("\n🌟 NEW CLI"));
    log(chalk.gray("━".repeat(50)));

    try {
        // 1. 检查版本更新
        const latestVersion = await checkVersion();
        if (latestVersion && latestVersion.match(/^\d+\.\d+\.\d+$/) && latestVersion !== packageJson.version) {
            const rl = readline.createInterface({ input: process.stdin, output: process.stdout });
            const answer = await new Promise(resolve => {
                rl.question(chalk.yellow(`⚠️ 检测到新版本: ${latestVersion}，是否更新? (y/n): `), resolve);
            });
            rl.close();

            if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
                const installUrl = "https://code.yoretea.com/install";
                console.log(chalk.cyan("🔄 正在更新..."));
                const updateProcess = spawn("npm", ["install", "-g", installUrl], { stdio: ["inherit", "inherit", "pipe"] });
                let stderr = '';
                updateProcess.stderr.on('data', (data) => {
                    stderr += data.toString();
                    process.stderr.write(data);
                });
                updateProcess.on('close', async () => {
                    if (updateProcess.exitCode === 0) {
                        console.log(chalk.green("🎉 更新成功!"));
                    } else {
                        console.error(chalk.red("❌ 更新失败，请手动更新"));
                        console.error(chalk.yellow(`💡 请手动尝试使用 sudo npm install -g ${installUrl} 或联系管理员`));
                    }
                    const s = readline.createInterface({ input: process.stdin, output: process.stdout });
                    await new Promise(r => s.question("按回车键关闭: ", r));
                    s.close();
                    process.exit(0);
                });
                return; // 等待更新进程结束
            } else {
                log(chalk.green("💡 使用当前版本"));
            }
        } else if (latestVersion === packageJson.version) {
            log(chalk.green(`当前版本: ${packageJson.version}，已是最新版本`));
        }

        // 2. 检查并处理认证
        const existingAuth = await checkExistingAuth();
        if (existingAuth) {
            await onAuthenticated(existingAuth);
            return;
        }

        console.log(chalk.cyan("🔑 开始登录流程..."));
        console.log();
        try {
            const newAuth = await startLoginCodeFlow();
            await onAuthenticated(newAuth);
        } catch (error) {
            console.error(chalk.red("登录失败:"), error.message);
            process.exit(1);
        }

    } catch (error) {
        console.error(chalk.red("\n❌ 授权失败:"), error.message);
        process.exit(1);
    }
}


// --- 启动程序 ---
main().catch(error => {
    console.error(chalk.red("❌ 程序执行失败:"), error.message);
    process.exit(1);
});