{"name": "@anthropic-ai/claude-code", "version": "1.0.51", "main": "sdk.mjs", "types": "sdk.d.ts", "bin": {"claude": "start.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": "<PERSON> <<EMAIL>>", "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "dependencies": {"axios": "^1.9.0", "chalk": "^5.3.0", "commander": "^11.1.0", "express": "^5.1.0", "ink": "^4.4.1", "node-fetch": "^3.3.2", "open": "^10.1.2", "react": "^18.2.0", "uuid": "^11.1.0"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}}